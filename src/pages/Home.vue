<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "2023娱乐厅年度盛典"
    }
}
</route>

<template>
    <page-container>
        <div class="home">
            <!-- 顶部横幅 -->
            <div class="banner-section">
                <div class="banner-bg">
                    <img
                        class="banner-bg-1"
                        :src="requireImg('banner_bg_1.png')"
                        alt="" />
                    <img
                        class="banner-bg-2"
                        :src="requireImg('banner_bg_2.png')"
                        alt="" />
                    <img
                        class="banner-image-618"
                        :src="requireImg('banner_image_618.png')"
                        alt="" />
                    <img
                        class="banner-image-436"
                        :src="requireImg('banner_image_436.png')"
                        alt="" />
                    <div class="banner-gradient"></div>
                    <div class="banner-content">
                        <h1 class="banner-title">2023娱乐厅年度盛典</h1>
                        <p class="banner-subtitle">逐梦无惧 巅峰加冕</p>
                    </div>
                </div>
            </div>

            <!-- 倒计时区域 -->
            <div class="countdown-section">
                <div class="countdown-container">
                    <img
                        class="countdown-bg"
                        :src="requireImg('img_countdown_bg.png')"
                        alt="" />
                    <div class="countdown-content">
                        <span class="countdown-label">距离主赛程开始</span>
                        <div class="countdown-time">
                            <div class="time-block">
                                <span class="time-number">{{ countdownData.days }}</span>
                            </div>
                            <span class="time-unit">天</span>
                            <div class="time-block">
                                <span class="time-number">{{ countdownData.hours }}</span>
                            </div>
                            <span class="time-unit">时</span>
                            <div class="time-block">
                                <span class="time-number">{{ countdownData.minutes }}</span>
                            </div>
                            <span class="time-unit">分</span>
                            <div class="time-block">
                                <span class="time-number">{{ countdownData.seconds }}</span>
                            </div>
                            <span class="time-unit">秒</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报名入口区域 -->
            <div class="registration-section">
                <div class="section-bg">
                    <img
                        class="block-bg-1"
                        :src="requireImg('img_block_bg_1.png')"
                        alt="" />
                    <img
                        class="block-bg-2"
                        :src="requireImg('img_block_bg_2.png')"
                        alt="" />
                    <img
                        class="block-bg-3"
                        :src="requireImg('img_block_bg_3.png')"
                        alt="" />
                </div>

                <!-- 标题 -->
                <div class="section-title">
                    <img
                        class="title-bg"
                        :src="requireImg('img_title_bg.svg')"
                        alt="" />
                    <span class="title-text">报名入口</span>
                </div>

                <!-- 公会赛 -->
                <div class="competition-item">
                    <img
                        class="competition-icon"
                        :src="requireImg('img_gonghuisai_01.svg')"
                        alt="" />
                    <div class="competition-info">
                        <span class="competition-time">报名时间：12月8日~14日</span>
                    </div>
                    <button
                        class="competition-btn"
                        @click="handleGuildRegistration">
                        <img
                            :src="requireImg('img_btn_gonghuisai_01.svg')"
                            alt="" />
                        <span class="btn-text">工会报名</span>
                    </button>
                </div>

                <!-- 个人赛 -->
                <div class="competition-item">
                    <img
                        class="competition-icon"
                        :src="requireImg('img_gonghuisai_02.svg')"
                        alt="" />
                    <div class="competition-info">
                        <span class="competition-time">报名时间：12月8日~14日</span>
                    </div>
                    <button
                        class="competition-btn"
                        @click="handlePersonalRegistration">
                        <img
                            :src="requireImg('img_btn_gonghuisai_02.svg')"
                            alt="" />
                        <span class="btn-text">打卡签到</span>
                    </button>
                </div>

                <!-- 规则说明 -->
                <div
                    class="rule-guide"
                    @click="showRuleGuide">
                    <div class="rule-icon">?</div>
                    <span class="rule-text">规则说明></span>
                </div>
            </div>

            <!-- 盛典大咖 全民星探区域 -->
            <div class="celebrity-section">
                <div class="section-bg">
                    <img
                        class="block-bg-1"
                        :src="requireImg('img_block_bg_1.png')"
                        alt="" />
                    <img
                        class="block-bg-2"
                        :src="requireImg('img_block_bg_2.png')"
                        alt="" />
                    <img
                        class="block-bg-3"
                        :src="requireImg('img_block_bg_3.png')"
                        alt="" />
                </div>

                <!-- 标题 -->
                <div class="section-title">
                    <img
                        class="title-bg"
                        :src="requireImg('img_title_bg.svg')"
                        alt="" />
                    <span class="title-text">盛典大咖</span>
                </div>

                <!-- 轮播图 -->
                <div class="celebrity-swipe">
                    <van-swipe
                        :autoplay="3000"
                        :loop="true"
                        indicator-color="rgba(255, 255, 255, 0.3)"
                        :show-indicators="true">
                        <van-swipe-item
                            v-for="(celebrity, index) in celebrityList"
                            :key="index">
                            <div class="celebrity-card">
                                <div class="celebrity-avatar">
                                    <img
                                        :src="celebrity.avatar"
                                        :alt="celebrity.name" />
                                </div>
                                <div class="celebrity-info">
                                    <h3 class="celebrity-name">{{ celebrity.name }}</h3>
                                    <p class="celebrity-title">{{ celebrity.title }}</p>
                                    <div class="celebrity-stats">
                                        <span class="stat-item">粉丝: {{ celebrity.fans }}</span>
                                        <span class="stat-item">等级: {{ celebrity.level }}</span>
                                    </div>
                                </div>
                                <div class="celebrity-badge">
                                    <span>大咖</span>
                                </div>
                            </div>
                        </van-swipe-item>
                    </van-swipe>
                </div>

                <div class="celebrity-subtitle">
                    <span>全民星探 发现更多精彩</span>
                </div>
            </div>

            <!-- 荣耀打卡区域 -->
            <div class="honor-checkin-section">
                <div class="checkin-bg">
                    <img
                        class="checkin-bg-image"
                        :src="requireImg('img_ry_daka_bg.png')"
                        alt="" />
                </div>

                <div class="checkin-content">
                    <div class="checkin-gifts">
                        <div class="gift-item">
                            <img
                                class="gift-image"
                                :src="requireImg('img_ry_daka_gift_01_b.png')"
                                alt="" />
                        </div>
                        <div class="gift-item">
                            <img
                                class="gift-image"
                                :src="requireImg('img_ry_daka_gift_01_a.png')"
                                alt="" />
                        </div>
                    </div>

                    <div class="checkin-progress">
                        <div class="progress-bar">
                            <div
                                class="progress-fill"
                                :style="{ width: `${checkinProgress}%` }"></div>
                        </div>
                        <div class="progress-text">{{ checkinDays }}/7天</div>
                    </div>

                    <button
                        class="checkin-btn"
                        @click="handleCheckin">
                        <span>立即打卡</span>
                    </button>
                </div>
            </div>

            <!-- 排行榜区域 -->
            <div class="ranking-section">
                <div class="section-bg">
                    <img
                        class="block-bg-1"
                        :src="requireImg('img_block_bg_1.png')"
                        alt="" />
                    <img
                        class="block-bg-2"
                        :src="requireImg('img_block_bg_2.png')"
                        alt="" />
                    <img
                        class="block-bg-3"
                        :src="requireImg('img_block_bg_3.png')"
                        alt="" />
                </div>

                <div class="ranking-tabs">
                    <button
                        class="tab-btn"
                        :class="{ active: activeTab === 'yesterday' }"
                        @click="switchTab('yesterday')">
                        昨日MVP
                    </button>
                    <button
                        class="tab-btn"
                        :class="{ active: activeTab === 'ranking' }"
                        @click="switchTab('ranking')">
                        荣耀榜单
                    </button>
                </div>

                <div class="ranking-subtitle">
                    <span>工会争霸 坐享名利双收 12月8日 18:00 ~ 12月27日 23:59</span>
                    <span class="ranking-rule">奖励&规则></span>
                </div>

                <!-- 昨日MVP -->
                <div
                    v-if="activeTab === 'yesterday'"
                    class="yesterday-mvp">
                    <div class="mvp-item">
                        <img
                            class="mvp-no1"
                            :src="requireImg('img_yesterday_no1.svg')"
                            alt="" />
                        <div class="mvp-info">
                            <div class="mvp-avatar">
                                <img
                                    :src="requireImg('default_avatar_no_compress.png')"
                                    alt="" />
                            </div>
                            <div class="mvp-details">
                                <div class="mvp-name">玩家昵称</div>
                                <div class="mvp-score">积分: 12,345</div>
                            </div>
                            <img
                                class="mvp-badge"
                                :src="requireImg('img_yesterday_mvp.svg')"
                                alt="" />
                        </div>
                        <button class="mvp-btn">
                            <img
                                :src="requireImg('img_yesterday_btn.svg')"
                                alt="" />
                            <span>查看详情</span>
                        </button>
                    </div>
                </div>

                <!-- 荣耀榜单 -->
                <div
                    v-if="activeTab === 'ranking'"
                    class="honor-ranking">
                    <!-- TOP3 使用ttweb知识库组件 -->
                    <div
                        v-if="top3.length > 0"
                        class="top3">
                        <!-- 第一名 -->
                        <Top1
                            v-if="top3[0]"
                            :data="top3[0]"
                            value-label="积分" />

                        <!-- 第二名和第三名 -->
                        <div
                            v-if="top3.length > 1"
                            class="top23-list relative mx-auto h-173 -mt-30">
                            <!-- 第二名 -->
                            <Top
                                v-if="top3[1]"
                                class="left-23.5 top-0 !absolute"
                                :data="top3[1]"
                                value-label="积分" />
                            <!-- 第三名 -->
                            <Top
                                v-if="top3[2]"
                                class="right-23.5 top-0 !absolute"
                                :data="top3[2]"
                                value-label="积分" />
                        </div>
                    </div>

                    <!-- 其他名次使用rank-item组件 -->
                    <div
                        v-if="restList.length > 0"
                        class="rank-list-content mt-11">
                        <RankItem
                            v-for="item in restList"
                            :key="item?.userInfo?.uid"
                            :data="item"
                            value-label="积分" />
                    </div>
                </div>
            </div>

            <!-- 底部说明 -->
            <div class="footer-note">
                <div class="note-line"></div>
                <span class="note-text">活动最终解释权归平台所有</span>
                <div class="note-line"></div>
            </div>
        </div>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';
import Top1 from '@/components/rank/top1.vue';
import Top from '@/components/rank/top.vue';
import RankItem from '@/components/rank/rank-item.vue';

const router = useRouter();
const initStore = useInitStore();

// 倒计时数据
const countdownData = reactive({
    days: '24',
    hours: '24',
    minutes: '59',
    seconds: '59',
});

// 打卡数据
const checkinProgress = ref(60); // 打卡进度百分比
const checkinDays = ref(4); // 已打卡天数

// 盛典大咖数据
const celebrityList = ref([
    {
        name: '星光女神',
        title: '人气主播',
        avatar: requireImg('default_avatar_no_compress.png'),
        fans: '128.5万',
        level: 'VIP8',
    },
    {
        name: '音乐王子',
        title: '才艺达人',
        avatar: requireImg('default_avatar_no_compress.png'),
        fans: '96.3万',
        level: 'VIP7',
    },
    {
        name: '舞蹈精灵',
        title: '舞蹈女王',
        avatar: requireImg('default_avatar_no_compress.png'),
        fans: '85.7万',
        level: 'VIP6',
    },
]);

// 排行榜数据
const activeTab = ref('ranking'); // 当前激活的标签页，默认显示荣耀榜单
const rankingList = ref([
    { name: '玩家1', score: '15,678', level: 'VIP5' },
    { name: '玩家2', score: '14,523', level: 'VIP4' },
    { name: '玩家3', score: '13,456', level: 'VIP3' },
    { name: '玩家4', score: '12,789', level: 'VIP2' },
    { name: '玩家5', score: '11,234', level: 'VIP1' },
    { name: '玩家6', score: '10,567', level: 'VIP1' },
    { name: '玩家7', score: '9,876', level: 'VIP1' },
]);

// 转换为ttweb知识库组件格式的数据
const convertedRankingData = computed(() => {
    return rankingList.value.map((item, index) => ({
        rank: index + 1,
        rankHuman: (index + 1).toString(),
        userInfo: {
            uid: `demo_${index + 1}`,
            username: `user_${index + 1}`,
            nickname: item.name,
        },
        channelInfo: {
            status: Math.random() > 0.5 ? 1 : 0, // 随机在线状态
            channelId: `channel_${index + 1}`,
        },
        value: Number.parseInt(item.score.replace(',', '')),
    }));
});

// 榜单前三
const top3 = computed(() => {
    return convertedRankingData.value.slice(0, 3);
});

// 剩余的榜单列表
const restList = computed(() => {
    return convertedRankingData.value.slice(3);
});

// 倒计时逻辑
let countdownTimer = null;

function startCountdown() {
    // 设置目标时间（这里可以根据实际需求设置）
    const targetDate = new Date('2024-12-15 00:00:00').getTime();

    countdownTimer = setInterval(() => {
        const now = new Date().getTime();
        const distance = targetDate - now;

        if (distance > 0) {
            countdownData.days = Math.floor(distance / (1000 * 60 * 60 * 24)).toString().padStart(2, '0');
            countdownData.hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)).toString().padStart(2, '0');
            countdownData.minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
            countdownData.seconds = Math.floor((distance % (1000 * 60)) / 1000).toString().padStart(2, '0');
        }
        else {
            clearInterval(countdownTimer);
        }
    }, 1000);
}

// 事件处理函数
function handleGuildRegistration() {
    showToast('工会报名功能开发中');
    // 这里添加工会报名逻辑
}

function handlePersonalRegistration() {
    showToast('个人报名功能开发中');
    // 这里添加个人报名逻辑
}

function handleCheckin() {
    if (checkinDays.value < 7) {
        checkinDays.value++;
        checkinProgress.value = (checkinDays.value / 7) * 100;
        showToast('打卡成功！');
    }
    else {
        showToast('本周打卡已完成');
    }
}

function switchTab(tab) {
    activeTab.value = tab;
}

function getRankingBadge(rank) {
    switch (rank) {
        case 1:
            return requireImg('img_ry_bangdan_no1.svg');
        case 2:
            return requireImg('img_ry_bangdan_no2.svg');
        case 3:
            return requireImg('img_ry_bangdan_no3.svg');
        default:
            return requireImg('img_ry_bangdan_no3.svg'); // 默认使用第三名样式
    }
}

function showRuleGuide() {
    router.push('/rule');
}

onMounted(async () => {
    const toast = showLoading();
    await initStore.init();
    startCountdown();
    toast.close();
});

onUnmounted(() => {
    if (countdownTimer) {
        clearInterval(countdownTimer);
    }
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100vh;
    background: linear-gradient(180deg, #1d1032 0%, #361d59 100%);
    position: relative;
    overflow-x: hidden;
}

/* 顶部横幅样式 */
.banner-section {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.banner-bg {
    position: relative;
    width: 100%;
    height: 100%;

    .banner-bg-1,
    .banner-bg-2 {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .banner-image-618 {
        position: absolute;
        top: 50px;
        left: 50%;
        transform: translateX(-50%);
        width: 242px;
        height: 70px;
        object-fit: contain;
    }

    .banner-image-436 {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 247px;
        height: 30px;
        object-fit: contain;
    }

    .banner-gradient {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: linear-gradient(180deg, rgba(29, 16, 50, 0) 0%, rgba(29, 16, 50, 1) 100%);
    }
}

.banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;

    .banner-title {
        font-family: 'HYYakuHei', sans-serif;
        font-weight: 900;
        font-size: 24px;
        line-height: 1.2;
        color: #ffffff;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        margin: 0 0 8px 0;
        text-stroke: 1px #ffd668;
        -webkit-text-stroke: 1px #ffd668;
    }

    .banner-subtitle {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 1.2;
        color: #ffffff;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        margin: 0;
        text-stroke: 1px #ffd668;
        -webkit-text-stroke: 1px #ffd668;
    }
}

/* 倒计时区域样式 */
.countdown-section {
    padding: 20px;
    display: flex;
    justify-content: center;
}

.countdown-container {
    position: relative;
    width: 308px;
    height: 32px;

    .countdown-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .countdown-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .countdown-label {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 400;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .countdown-time {
            display: flex;
            align-items: center;
            gap: 4px;

            .time-block {
                width: 20px;
                height: 20px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
                display: flex;
                align-items: center;
                justify-content: center;

                .time-number {
                    font-family: 'PingFang SC', sans-serif;
                    font-weight: 600;
                    font-size: 12px;
                    color: #ffffff;
                }
            }

            .time-unit {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 400;
                font-size: 10px;
                color: rgba(255, 255, 255, 0.7);
            }
        }
    }
}

/* 报名入口区域样式 */
.registration-section {
    position: relative;
    padding: 20px;
    margin-top: 20px;
}

.section-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .block-bg-1,
    .block-bg-2,
    .block-bg-3 {
        position: absolute;
        width: 100%;
        object-fit: cover;
    }

    .block-bg-1 {
        top: 0;
        height: 70px;
    }

    .block-bg-2 {
        top: 60px;
        height: 88px;
    }

    .block-bg-3 {
        bottom: 0;
        height: 36px;
    }
}

/* 标题样式 */
.section-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    z-index: 10;

    .title-bg {
        position: absolute;
        width: 129px;
        height: 32px;
        object-fit: contain;
    }

    .title-text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 16px;
        color: #ffffff;
        z-index: 1;
    }
}

/* 竞赛项目样式 */
.competition-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    z-index: 10;

    .competition-icon {
        width: 140px;
        height: 90px;
        object-fit: contain;
        margin-bottom: 8px;
    }

    .competition-info {
        margin-bottom: 12px;

        .competition-time {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 400;
            font-size: 12px;
            color: #9e8ac8;
        }
    }

    .competition-btn {
        position: relative;
        width: 140px;
        height: 40px;
        border: none;
        background: transparent;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .btn-text {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 14px;
            color: #632a00;
            z-index: 1;
        }

        &:active {
            transform: scale(0.95);
        }
    }
}

/* 规则说明样式 */
.rule-guide {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
    cursor: pointer;
    opacity: 0.6;
    z-index: 10;

    .rule-icon {
        width: 20px;
        height: 20px;
        border: 1px solid #9e8ac8;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 12px;
        color: #9e8ac8;
    }

    .rule-text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 12px;
        color: #9e8ac8;
    }

    &:active {
        opacity: 0.8;
    }
}

/* 盛典大咖区域样式 */
.celebrity-section {
    position: relative;
    padding: 20px;
    margin-top: 20px;
}

.celebrity-swipe {
    margin: 20px 0;

    .celebrity-card {
        display: flex;
        align-items: center;
        padding: 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        margin: 0 10px;

        .celebrity-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .celebrity-info {
            flex: 1;

            .celebrity-name {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 600;
                font-size: 16px;
                color: #ffffff;
                margin: 0 0 4px 0;
            }

            .celebrity-title {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 400;
                font-size: 12px;
                color: #9e8ac8;
                margin: 0 0 8px 0;
            }

            .celebrity-stats {
                display: flex;
                gap: 16px;

                .stat-item {
                    font-family: 'PingFang SC', sans-serif;
                    font-weight: 400;
                    font-size: 11px;
                    color: rgba(255, 255, 255, 0.7);
                }
            }
        }

        .celebrity-badge {
            padding: 4px 12px;
            background: linear-gradient(90deg, #ffd668 0%, #ff8020 100%);
            border-radius: 12px;

            span {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 600;
                font-size: 12px;
                color: #632a00;
            }
        }
    }
}

.celebrity-subtitle {
    text-align: center;
    margin-top: 12px;

    span {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 12px;
        color: #9e8ac8;
    }
}

/* 荣耀打卡区域样式 */
.honor-checkin-section {
    position: relative;
    margin: 20px;
    border-radius: 16px;
    overflow: hidden;
}

.checkin-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .checkin-bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.checkin-content {
    position: relative;
    padding: 20px;
    z-index: 10;

    .checkin-gifts {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;

        .gift-item {
            .gift-image {
                width: 60px;
                height: 60px;
                object-fit: contain;
            }
        }
    }

    .checkin-progress {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;

        .progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #ffd668 0%, #ff8020 100%);
                border-radius: 4px;
                transition: width 0.3s ease;
            }
        }

        .progress-text {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 500;
            font-size: 12px;
            color: #ffffff;
        }
    }

    .checkin-btn {
        width: 100%;
        height: 40px;
        background: linear-gradient(90deg, #ffd668 0%, #ff8020 100%);
        border: none;
        border-radius: 20px;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 16px;
        color: #632a00;
        cursor: pointer;

        &:active {
            transform: scale(0.98);
        }
    }
}

/* 排行榜区域样式 */
.ranking-section {
    position: relative;
    margin: 20px;
    padding: 20px;
    border-radius: 16px;
    overflow: hidden;
}

.ranking-tabs {
    display: flex;
    background: rgba(25, 16, 47, 1);
    border-radius: 20px;
    padding: 4px;
    margin-bottom: 16px;
    z-index: 10;
    position: relative;

    .tab-btn {
        flex: 1;
        height: 36px;
        border: none;
        background: transparent;
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 18px;

        &.active {
            color: #ffffff;
            background: rgba(255, 255, 255, 0.1);
        }

        &:active {
            transform: scale(0.98);
        }
    }
}

.ranking-subtitle {
    text-align: center;
    margin-bottom: 20px;
    z-index: 10;
    position: relative;

    span {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 12px;
        color: #9e8ac8;
        display: block;
        margin-bottom: 4px;
    }

    .ranking-rule {
        color: #ffd668;
        cursor: pointer;

        &:hover {
            opacity: 0.8;
        }
    }
}

/* 昨日MVP样式 */
.yesterday-mvp {
    padding: 20px;

    .mvp-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .mvp-no1 {
            width: 323px;
            height: 120px;
            object-fit: contain;
            margin-bottom: 16px;
        }

        .mvp-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;

            .mvp-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .mvp-details {
                flex: 1;

                .mvp-name {
                    font-family: 'PingFang SC', sans-serif;
                    font-weight: 500;
                    font-size: 14px;
                    color: #ffffff;
                    margin-bottom: 4px;
                }

                .mvp-score {
                    font-family: 'PingFang SC', sans-serif;
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.7);
                }
            }

            .mvp-badge {
                width: 34px;
                height: 48px;
                object-fit: contain;
            }
        }

        .mvp-btn {
            position: relative;
            width: 80px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            span {
                font-family: 'PingFang SC', sans-serif;
                font-weight: 500;
                font-size: 12px;
                color: #ffffff;
                z-index: 1;
            }

            &:active {
                transform: scale(0.95);
            }
        }
    }
}

/* 荣耀榜单样式 - 使用ttweb知识库组件 */
.honor-ranking {
    position: relative;
    z-index: 10;

    .top3 {
        /* TOP1独占一行的样式 */
    }

    .top23-list {
        /* TOP2和TOP3并排显示的容器样式 */
        width: 270px;
    }

    .rank-list-content {
        /* 第4名及以后的列表样式 */
        width: 375px;
        min-height: 200px;
        background: url('@/assets/img/tab3/<EMAIL>') no-repeat center top;
        background-size: 375px 706.5px;
    }
}

/* 底部说明样式 */
.footer-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 40px 20px 20px;

    .note-line {
        flex: 1;
        height: 1px;
        background: rgba(255, 255, 255, 0.2);
    }

    .note-text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        white-space: nowrap;
    }
}

/* 移动端适配 */
@media (max-width: 375px) {
    .banner-section {
        height: 300px;
    }

    .banner-content {
        .banner-title {
            font-size: 20px;
        }

        .banner-subtitle {
            font-size: 14px;
        }
    }

    .banner-bg {
        .banner-image-618 {
            width: 200px;
            height: 58px;
            top: 40px;
        }

        .banner-image-436 {
            width: 200px;
            height: 24px;
            bottom: 15px;
        }
    }

    .countdown-container {
        width: 280px;
        height: 28px;
    }

    .competition-item {
        .competition-icon {
            width: 120px;
            height: 77px;
        }

        .competition-btn {
            width: 120px;
            height: 34px;
        }
    }

    .yesterday-mvp {
        .mvp-item {
            .mvp-no1 {
                width: 280px;
                height: 104px;
            }
        }
    }

    .honor-checkin-section {
        margin: 15px;
    }

    .ranking-section {
        margin: 15px;
    }
}
</style>
