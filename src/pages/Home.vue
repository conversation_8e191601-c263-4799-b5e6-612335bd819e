<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "首页"
    }
}
</route>

<template>
    <page-container>
        <div class="home">
            首页
            <img
                class="block h-100 w-100 bg-coolGray"
                :src="requireImg('default_avatar_no_compress.png')"
                @click="toRule" />
            isEnd:{{ isEnd }}
            <br />
            market_id:{{ MARKET_ID }}
            <br />
            是否TT:{{ MARKET_ID === MARKET_ID_MAP.TT }}
            <br />
            initData:{{ initData }}
            <br />
            导入public目录下静态资源链接，lottie/GE动效json可用：{{ requirePublic('/apm-sdk.js') }}
        </div>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';

const router = useRouter();
const initStore = useInitStore();

const toRule = () => {
    router.push('/rule');
    // 打开分享弹窗
    useEventBus('share-modal').emit({ show: true });
};

onMounted(async () => {
    const toast = showLoading();
    await initStore.init();
    toast.close();
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100%;
    background-color: skyblue;
}
</style>
