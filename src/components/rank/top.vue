<!-- src/components/rank/top.vue -->
<template>
    <div
        class="top relative mx-auto h-[173.5px] w-[123.5px]"
        :class="`top${props.data?.rank}`">
        <img
            class="absolute left-0 top-0 h-full w-full"
            :src="bgImage" />
        <div
            class="avatar absolute left-1/2 top-41 h-55 w-55 rounded-full -translate-x-1/2"
            @click="handleToPerson">
            <img
                class="h-full w-full rounded-full object-cover"
                :src="avatarUrl"
                alt="avatar" />
            <RoomStatus
                class="absolute left-1/2 -bottom-2 -translate-x-1/2"
                :channel-info="props.data?.channelInfo" />
        </div>
        <div class="name absolute left-1/2 top-107 w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            <span class="name-text whitespace-nowrap">{{ displayNickname }}</span>
        </div>
        <div class="value-label absolute left-1/2 top-125 w-full flex justify-center whitespace-nowrap -translate-x-1/2">{{ props.valueLabel }}</div>
        <div class="eternal-value absolute left-1/2 top-140 w-full flex justify-center whitespace-nowrap whitespace-nowrap -translate-x-1/2">
            {{ displayValue }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import RoomStatus from './room-status.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            rank: 2,
            userInfo: {
                uid: '2646810',
                username: '2646810',
                nickname: '第二名用户昵称',
            },
            channelInfo: {
                status: 1,
                channelId: '264681033',
            },
            value: 98600,
        }),
    },
    valueLabel: {
        type: String,
        default: '永恒值',
    },
});

const RANK_TO_BG = {
    1: 'img_ry_bangdan_no1.svg',
    2: 'img_ry_bangdan_no2.svg',
    3: 'img_ry_bangdan_no3.svg',
};

const bgImage = computed(() => {
    return requireImg(RANK_TO_BG[props.data?.rank] || RANK_TO_BG[2]);
});

const displayNickname = computed(() => {
    return omitTxt(props.data?.userInfo?.nickname, 7);
});

const displayValue = computed(() => {
    return omitValue(props.data?.value || 0);
});

const avatarUrl = computed(() => {
    return getAvatar(props.data?.userInfo?.username);
});

function handleToPerson() {
    toPerson(props.data?.userInfo?.username);
}
</script>

<style lang="less" scoped>
.top {
    .name {
        .name-text {
            text-shadow:
                0 1px 1px #427dff,
                1px 0 1px #427dff,
                0 -1px 1px #427dff,
                -1px 0 1px #427dff,
                1px 1px 1px #427dff,
                -1px -1px 1px #427dff,
                1px -1px 1px #427dff,
                -1px 1px 1px #427dff;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 12px;
            font-family: FZLanTingHeiS-R-GB, FZLanTingHeiS-R-GB-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            line-height: 13px;
            letter-spacing: -0.24px;
        }
    }
    .value-label {
        font-size: 10px;
        font-family:
            Adobe Heiti Std,
            Adobe Heiti Std-R;
        font-weight: normal;
        text-align: left;
        color: #ffffff;
        line-height: 11px;
        letter-spacing: -0.2px;
    }
    .eternal-value {
        font-size: 12px;
        font-family: FZLanTingHeiS-R-GB, FZLanTingHeiS-R-GB-Regular;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 13px;
        letter-spacing: -0.24px;
    }
}
.top1 {
    .name {
        .name-text {
            text-shadow:
                0 1px 1px #427dff,
                1px 0 1px #427dff,
                0 -1px 1px #427dff,
                -1px 0 1px #427dff,
                1px 1px 1px #427dff,
                -1px -1px 1px #427dff,
                1px -1px 1px #427dff,
                -1px 1px 1px #427dff !important;
        }
    }
}
.top2 {
    .name {
        .name-text {
            text-shadow:
                0 1px 1px #a552ff,
                1px 0 1px #a552ff,
                0 -1px 1px #a552ff,
                -1px 0 1px #a552ff,
                1px 1px 1px #a552ff,
                -1px -1px 1px #a552ff,
                1px -1px 1px #a552ff,
                -1px 1px 1px #a552ff !important;
        }
    }
}
.top3 {
    .name {
        .name-text {
            text-shadow:
                0 1px 1px #ff5252,
                1px 0 1px #ff5252,
                0 -1px 1px #ff5252,
                -1px 0 1px #ff5252,
                1px 1px 1px #ff5252,
                -1px -1px 1px #ff5252,
                1px -1px 1px #ff5252,
                -1px 1px 1px #ff5252 !important;
        }
    }
}
</style>
