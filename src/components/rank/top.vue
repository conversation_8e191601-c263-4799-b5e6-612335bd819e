<!-- src/components/rank/top.vue -->
<template>
    <div
        class="top relative mx-auto w-[124px] h-[230px]"
        :class="`top${props.data?.rank}`">
        <img
            class="absolute left-0 top-0 h-full w-full"
            :src="bgImage" />
        <div
            class="avatar absolute left-1/2 top-[48.96px] w-[44px] h-[44px] rounded-full -translate-x-1/2"
            @click="handleToPerson">
            <img
                class="h-full w-full rounded-full object-cover"
                :src="avatarUrl"
                alt="avatar" />
            <RoomStatus
                class="absolute left-1/2 -bottom-2 -translate-x-1/2"
                :channel-info="props.data?.channelInfo" />
        </div>
        <div class="name absolute left-1/2 top-[110px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            <span class="name-text whitespace-nowrap">{{ displayNickname }}</span>
        </div>
        <div class="value-label absolute left-1/2 top-[130px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">{{ props.valueLabel }}</div>
        <div class="eternal-value absolute left-1/2 top-[150px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            {{ displayValue }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import RoomStatus from './room-status.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            rank: 2,
            userInfo: {
                uid: '2646810',
                username: '2646810',
                nickname: '第二名用户昵称',
            },
            channelInfo: {
                status: 1,
                channelId: '264681033',
            },
            value: 98600,
        }),
    },
    valueLabel: {
        type: String,
        default: '永恒值',
    },
});

const RANK_TO_BG = {
    1: 'img_ry_bangdan_no1.svg',
    2: 'img_ry_bangdan_no2.svg',
    3: 'img_ry_bangdan_no3.svg',
};

const bgImage = computed(() => {
    return requireImg(RANK_TO_BG[props.data?.rank] || RANK_TO_BG[2]);
});

const displayNickname = computed(() => {
    return omitTxt(props.data?.userInfo?.nickname, 7);
});

const displayValue = computed(() => {
    return omitValue(props.data?.value || 0);
});

const avatarUrl = computed(() => {
    return getAvatar(props.data?.userInfo?.username);
});

function handleToPerson() {
    toPerson(props.data?.userInfo?.username);
}
</script>

<style lang="less" scoped>
.top {
    .name {
        .name-text {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 11px;
            color: #ffffff;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
    }
    .value-label {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 9px;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
    }
    .eternal-value {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 11px;
        color: #ffffff;
        text-align: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
}
</style>
