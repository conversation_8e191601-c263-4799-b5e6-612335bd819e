<!-- src/components/rank/rank-item.vue -->
<template>
    <div class="rank-item mx-auto mb-2 h-[80px] w-[331px] flex items-center px-4">
        <div class="rank-num h-6 w-6 flex shrink-0 items-center justify-center whitespace-nowrap">{{ props.data?.rankHuman }}</div>
        <div
            class="relative ml-3 h-[36px] w-[36px] shrink-0 rounded-full"
            @click="handleToPerson">
            <img
                class="h-full w-full rounded-full object-cover"
                :src="avatarUrl"
                alt="avatar"
            />
            <RoomStatus
                class="absolute -bottom-1 -right-1"
                :channel-info="props.data?.channelInfo" />
        </div>
        <div class="nickname ml-3 flex-1 whitespace-nowrap">{{ displayNickname }}</div>
        <div class="eternal-value flex flex-col items-end">
            <div class="eternal-value-title whitespace-nowrap">{{ props.valueLabel }}</div>
            <div class="eternal-value-value mt-1 whitespace-nowrap">{{ displayValue }}</div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import RoomStatus from './room-status.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            rankHuman: '1',
            userInfo: {
                uid: 'demo_001',
                username: 'demo_user',
                nickname: '示例用户',
            },
            channelInfo: {
                status: 1,
                channelId: 'demo_channel',
            },
            value: 12800,
        }),
    },
    valueLabel: {
        type: String,
        default: '永恒值',
    },
});

const displayNickname = computed(() => {
    return omitTxt(props.data?.userInfo?.nickname, 10);
});

const displayValue = computed(() => {
    return omitValue(props.data?.value || 0);
});

const avatarUrl = computed(() => {
    return getAvatar(props.data?.userInfo?.username);
});

function handleToPerson() {
    toPerson(props.data?.userInfo?.username);
}
</script>

<style lang="less" scoped>
.rank-item {
    background: rgba(48, 31, 72, 1);
    border-radius: 11px;

    .rank-num {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 14px;
        color: #ffffff;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
    }
    .nickname {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
    }
    .eternal-value {
        .eternal-value-title {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 400;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-align: right;
        }
        .eternal-value-value {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 12px;
            color: #ffffff;
            text-align: right;
        }
    }
}
</style>
