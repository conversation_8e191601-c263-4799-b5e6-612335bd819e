<!-- src/components/rank/room-status.vue -->
<template>
    <div
        v-if="isInRoom"
        class="room-status w-40 flex shrink-0 grow-0 items-center justify-center whitespace-nowrap"
        @click.stop="toRoom(props.channelInfo?.channelId)">
        <img
            class="w-full"
            src="https://obs-cdn.52tt.com/tt/fe-moss/web/20250729145107_63415642.png"
            alt="">
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    channelInfo: {
        type: Object,
        default: () => ({}),
    },
});

const isInRoom = computed(() => {
    return !!props.channelInfo?.status;
});
</script>

<style lang="less" scoped>
.room-status {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 12px;
    font-family:
        Alibaba PuHuiTi,
        Alibaba PuHuiTi-Regular;
    font-weight: 400;
    text-align: center;
    color: #ffffff;
    line-height: 15px;
    letter-spacing: -0.24px;
}
</style>
