<!-- src/components/rank/top1.vue -->
<template>
    <div class="top1 relative mx-auto w-[157.5px] h-[255px]">
        <img
            class="absolute left-0 top-0 h-full w-full"
            :src="bgImage" />
        <div
            class="avatar absolute left-1/2 top-[67.96px] w-[50px] h-[50px] rounded-full -translate-x-1/2"
            @click="handleToPerson">
            <img
                class="h-full w-full rounded-full object-cover"
                :src="avatarUrl"
                alt="avatar" />
            <RoomStatus
                class="absolute left-1/2 -bottom-2 -translate-x-1/2"
                :channel-info="props.data?.channelInfo" />
        </div>
        <!-- 用户名 -->
        <div class="name absolute left-1/2 top-[130px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            <span class="name-text whitespace-nowrap">{{ displayNickname }}</span>
        </div>
        <div class="value-label absolute left-1/2 top-[150px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">{{ props.valueLabel }}</div>
        <div class="eternal-value absolute left-1/2 top-[170px] w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            {{ displayValue }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import RoomStatus from './room-status.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            rank: 1,
            userInfo: {
                uid: '2646810',
                username: '2646810',
                nickname: '第一名用户昵称',
            },
            channelInfo: {
                status: 1,
                channelId: 'top1_channel',
            },
            value: 156800,
        }),
    },
    valueLabel: {
        type: String,
        default: '永恒值',
    },
});

const displayNickname = computed(() => {
    return omitTxt(props.data?.userInfo?.nickname, 7);
});

const displayValue = computed(() => {
    return omitValue(props.data?.value || 0);
});

const avatarUrl = computed(() => {
    return getAvatar(props.data?.userInfo?.username);
});

const bgImage = computed(() => {
    return requireImg('img_ry_bangdan_no1.svg');
});

function handleToPerson() {
    toPerson(props.data?.userInfo?.username);
}
</script>

<style lang="less" scoped>
.top1 {
    .name {
        .name-text {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 12px;
            color: #ffffff;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
    }
    .value-label {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 10px;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
    }
    .eternal-value {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
}
</style>
