<!-- src/components/rank/top1.vue -->
<template>
    <div class="top1 relative mx-auto h-[205.5px] w-[356px]">
        <img
            class="absolute left-0 top-0 h-full w-full"
            :src="bgImage" />
        <div
            class="avatar absolute left-140 top-50 h-70 w-70 rounded-full"
            @click="handleToPerson">
            <img
                class="h-full w-full rounded-full object-cover"
                :src="avatarUrl"
                alt="avatar" />
            <RoomStatus
                class="absolute left-1/2 -bottom-2 -translate-x-1/2"
                :channel-info="props.data?.channelInfo" />
        </div>
        <!-- 用户名 -->
        <div class="name absolute left-1/2 top-136 w-full flex justify-center whitespace-nowrap -translate-x-1/2">
            <span class="name-text whitespace-nowrap">{{ displayNickname }}</span>
        </div>
        <div class="value-label absolute left-1/2 top-154 w-full flex justify-center whitespace-nowrap -translate-x-1/2">{{ props.valueLabel }}</div>
        <div class="eternal-value absolute left-1/2 top-169 w-full flex justify-center whitespace-nowrap whitespace-nowrap -translate-x-1/2">
            {{ displayValue }}
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import RoomStatus from './room-status.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            rank: 1,
            userInfo: {
                uid: '2646810',
                username: '2646810',
                nickname: '第一名用户昵称',
            },
            channelInfo: {
                status: 1,
                channelId: 'top1_channel',
            },
            value: 156800,
        }),
    },
    valueLabel: {
        type: String,
        default: '永恒值',
    },
});

const displayNickname = computed(() => {
    return omitTxt(props.data?.userInfo?.nickname, 7);
});

const displayValue = computed(() => {
    return omitValue(props.data?.value || 0);
});

const avatarUrl = computed(() => {
    return getAvatar(props.data?.userInfo?.username);
});

const bgImage = computed(() => {
    return requireImg('img_ry_bangdan_no1.svg');
});

function handleToPerson() {
    toPerson(props.data?.userInfo?.username);
}
</script>

<style lang="less" scoped>
.top1 {
    .name {
        .name-text {
            text-shadow:
                0 1px 0 #ff7428,
                0 -1px 0 #ff7428,
                1px 0 0 #ff7428,
                -1px 0 0 #ff7428,
                1px 1px 0 #ff7428,
                -1px -1px 0 #ff7428,
                1px -1px 0 #ff7428,
                -1px 1px 0 #ff7428;
            font-size: 12px;
            font-family: FZLanTingHeiS-R-GB, FZLanTingHeiS-R-GB-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            line-height: 13px;
            letter-spacing: -0.24px;
        }
    }
    .value-label {
        font-size: 10px;
        font-family:
            Adobe Heiti Std,
            Adobe Heiti Std-R;
        font-weight: normal;
        text-align: left;
        color: #ffffff;
        line-height: 11px;
        letter-spacing: -0.2px;
    }
    .eternal-value {
        font-size: 12px;
        font-family: FZLanTingHeiS-R-GB, FZLanTingHeiS-R-GB-Regular;
        font-weight: 400;
        text-align: center;
        color: #9517ed;
        line-height: 13px;
        letter-spacing: -0.24px;
    }
}
</style>
